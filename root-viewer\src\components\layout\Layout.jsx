import { useState, useEffect } from 'react';
import Sidebar from '../sidebar/Sidebar';
import {
  fetchDPBasicData,
  formatDataSize,
  formatBooleanValue,
  getRunStateClass,
  getRunStateText
} from '../../services/apiService';
import './Layout.css';

const Layout = ({ children }) => {
  const [activeMenuItem, setActiveMenuItem] = useState(null);
  const [dpData, setDpData] = useState({
    run_state: true,
    error_message: "null",
    total_size: "1MB",
    data_assembled_id: "1",
    event_number: 10,
    if_compressed: false,
    stream_tag: "global_trigger",
    sidebar: null
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleMenuItemClick = (itemId) => {
    setActiveMenuItem(itemId);
  };

  // 获取DP数据的函数
  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchDPBasicData();
      setDpData(data);
    } catch (err) {
      setError('获取数据失败: ' + err.message);
      console.error('获取DP数据失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchData();
  }, []);

  // 监听sidebar数据变化，动态设置activeMenuItem
  useEffect(() => {
    if (dpData.sidebar && Object.keys(dpData.sidebar).length > 0) {
      // 如果sidebar有数据，且当前没有选中项，则设置默认选中第一个可用项
      if (!activeMenuItem) {
        // 找到第一个在sidebar中存在的静态菜单项
        const staticMenuItems = [
          'CD-LPMT', 'WP-LPMT', 'SPMT-T/Q', 'TT', 'CD-T/Q', 'WP-T/Q',
          'MM(Multi-Message)', 'LOW_E', 'TQ_TAO_CD', 'TQ_TAO_TVT', 'TQ_TAO_WT', 'TQ_TAO_CD_FEC'
        ];
        const firstAvailableItem = staticMenuItems.find(item => dpData.sidebar[item]);
        if (firstAvailableItem) {
          setActiveMenuItem(firstAvailableItem);
        }
      }
    } else {
      // 如果sidebar为空或不存在，清除选中状态
      setActiveMenuItem(null);
    }
  }, [dpData.sidebar, activeMenuItem]);

  // 处理更新DP数据按钮点击
  const handleUpdateData = () => {
    fetchData();
  };

  return (
    <div className="layout">
      <Sidebar
        activeItem={activeMenuItem}
        onItemClick={handleMenuItemClick}
        sidebarData={dpData.sidebar}
      />

      <div className="layout__main">
        <header className="layout__header">
          <div className="layout__header-content">
            <div className="layout__title">
              <span className={`system-status ${getRunStateClass(dpData.run_state)}`}>
                {getRunStateText(dpData.run_state)}
              </span>
              <span className="dp-data-size">
                DP data size：{formatDataSize(dpData.total_size)}
              </span>
              <span className="data-assembled-id">
                Data assembled id：{dpData.data_assembled_id}
              </span>
              <span className="event-number">
                Event number：{dpData.event_number}
              </span>
              <span className="if-compressed">
                If compressed：{formatBooleanValue(dpData.if_compressed)}
              </span>
              <span className="stream-tag">
                Stream_tag: {dpData.stream_tag}
              </span>
            </div>
            <div className="layout__header-actions">
              <button
                className="layout__notification-btn"
                title="更新DP数据"
                onClick={handleUpdateData}
                disabled={loading}
              >
                {loading ? '更新中...' : '更新DP数据'}
              </button>
            </div>
          </div>
          {error && (
            <div className="error-message" style={{
              position: 'absolute',
              top: '100%',
              left: '30px',
              right: '30px',
              background: '#f8d7da',
              color: '#721c24',
              padding: '8px 16px',
              borderRadius: '4px',
              fontSize: '14px',
              zIndex: 1000
            }}>
              {error}
            </div>
          )}
        </header>
        
        <main className="layout__content">
          {children || <DefaultContent
            activeItem={activeMenuItem}
          />}
        </main>
      </div>
    </div>
  );
};


const DefaultContent = ({ activeItem }) => {
  const content = {
    'CD-LPMT': (
      <div className="page-content">
        <div className="content-layout">
          {/* 上半部分：数据流块 */}
          <div className="content-top">
            <div className="content-block content-block--full">
              <h3>数据流</h3>
              <div className="data-stream-container">
                <div className="data-stream-output">
                  <div className="stream-line">
                    <span className="timestamp">[2024-01-15 14:23:01]</span>
                    <span className="data-type">INFO</span>
                    <span className="message">数据流连接已建立</span>
                  </div>
                  <div className="stream-line">
                    <span className="timestamp">[2024-01-15 14:23:02]</span>
                    <span className="data-type">DATA</span>
                    <span className="message">接收数据包: {"{"}"id": 1001, "value": 42.5, "status": "normal"{"}"}</span>
                  </div>
                  <div className="stream-line">
                    <span className="timestamp">[2024-01-15 14:23:03]</span>
                    <span className="data-type">DATA</span>
                    <span className="message">接收数据包: {"{"}"id": 1002, "value": 38.2, "status": "normal"{"}"}</span>
                  </div>
                  <div className="stream-line">
                    <span className="timestamp">[2024-01-15 14:23:04]</span>
                    <span className="data-type">WARN</span>
                    <span className="message">数据延迟检测: 延迟 150ms</span>
                  </div>
                  <div className="stream-line">
                    <span className="timestamp">[2024-01-15 14:23:05]</span>
                    <span className="data-type">DATA</span>
                    <span className="message">接收数据包: {"{"}"id": 1003, "value": 45.1, "status": "normal"{"}"}</span>
                  </div>
                  <div className="stream-line">
                    <span className="timestamp">[2024-01-15 14:23:06]</span>
                    <span className="data-type">INFO</span>
                    <span className="message">数据处理完成，共处理 3 条记录</span>
                  </div>
                </div>
                <div className="stream-controls">
                  <div className="stream-status">
                    <span className="status-indicator active"></span>
                    <span className="status-text">输出正常</span>
                  </div>
                  <div className="stream-stats">
                    <span className="stat-item">速率: 2.3 msg/s</span>
                    <span className="stat-item">总计: 1,247 条</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 下半部分：一个块 */}
          <div className="content-bottom">
            <div className="content-block content-block--full">
              <h3>CD-LPMT - ROOT图</h3>
              <p>这里是CD-LPMT的详细监测内容和分析结果。您可以在这里查看完整的数据分析、图表展示和历史记录。</p>
              <div className="analysis-content">
                <div className="analysis-section">
                  <h4>数据趋势</h4>
                  <div className="chart-placeholder">
                    📈 图表区域 - 数据趋势分析
                  </div>
                </div>
                <div className="analysis-section">
                  <h4>关键指标</h4>
                  <div className="metrics-grid">
                    <div className="metric-item">
                      <span className="metric-label">通道数</span>
                      <span className="metric-value">10</span>
                    </div>
                    <div className="metric-item">
                      <span className="metric-label">时间戳</span>
                      <span className="metric-value">99.8%</span>
                    </div>
                    <div className="metric-item">
                      <span className="metric-label">错误数量</span>
                      <span className="metric-value">3</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    ),
    'CD-LPMT-0': (
      <div className="page-content">
        <h2>CD-LPMT - 0</h2>
        <p>这里是CD-LPMT子项0的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-1': (
      <div className="page-content">
        <h2>CD-LPMT - 1</h2>
        <p>这里是CD-LPMT子项1的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-2': (
      <div className="page-content">
        <h2>CD-LPMT - 2</h2>
        <p>这里是CD-LPMT子项2的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-3': (
      <div className="page-content">
        <h2>CD-LPMT - 3</h2>
        <p>这里是CD-LPMT子项3的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-4': (
      <div className="page-content">
        <h2>CD-LPMT - 4</h2>
        <p>这里是CD-LPMT子项4的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-5': (
      <div className="page-content">
        <h2>CD-LPMT - 5</h2>
        <p>这里是CD-LPMT子项5的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-6': (
      <div className="page-content">
        <h2>CD-LPMT - 6</h2>
        <p>这里是CD-LPMT子项6的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-7': (
      <div className="page-content">
        <h2>CD-LPMT - 7</h2>
        <p>这里是CD-LPMT子项7的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-8': (
      <div className="page-content">
        <h2>CD-LPMT - 8</h2>
        <p>这里是CD-LPMT子项8的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-9': (
      <div className="page-content">
        <h2>CD-LPMT - 9</h2>
        <p>这里是CD-LPMT子项9的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'WP-LPMT': (
      <div className="page-content">
        <h2>WP-LPMT</h2>
        <p>这里是WP-LPMT的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'SPMT-T/Q': (
      <div className="page-content">
        <h2>SPMT-T/Q</h2>
        <p>这里是SPMT-T/Q的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'TT': (
      <div className="page-content">
        <h2>TT</h2>
        <p>这里是TT的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-T/Q': (
      <div className="page-content">
        <h2>CD-T/Q</h2>
        <p>这里是CD-T/Q的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'WP-T/Q': (
      <div className="page-content">
        <h2>WP-T/Q</h2>
        <p>这里是WP-T/Q的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'MM(Multi-Message)': (
      <div className="page-content">
        <h2>MM(Multi-Message)</h2>
        <p>这里是MM(Multi-Message)的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'LOW_E': (
      <div className="page-content">
        <h2>LOW_E</h2>
        <p>这里是LOW_E的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'TQ_TAO_CD': (
      <div className="page-content">
        <h2>TQ_TAO_CD</h2>
        <p>这里是TQ_TAO_CD的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'TQ_TAO_TVT': (
      <div className="page-content">
        <h2>TQ_TAO_TVT</h2>
        <p>这里是TQ_TAO_TVT的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'TQ_TAO_WT': (
      <div className="page-content">
        <h2>TQ_TAO_WT</h2>
        <p>这里是TQ_TAO_WT的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'TQ_TAO_CD_FEC': (
      <div className="page-content">
        <h2>TQ_TAO_CD_FEC</h2>
        <p>这里是TQ_TAO_CD_FEC的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
  };

  // 如果没有选中项，显示提示信息
  if (!activeItem) {
    return (
      <div className="page-content">
        <div className="no-selection-message">
          <h2>请选择一个监测项目</h2>
          <p>从左侧侧边栏中选择一个项目来查看相关的监测数据和分析结果。</p>
        </div>
      </div>
    );
  }

  return content[activeItem] || content['CD-LPMT'];
};

export default Layout;
